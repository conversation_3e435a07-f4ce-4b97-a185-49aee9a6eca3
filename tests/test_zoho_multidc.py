"""
Test suite for Zoho Multi-DC OAuth functionality.

This test file verifies that the Zoho Multi-DC support works correctly
for handling users from different data centers.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import httpx
from typing import Dict, Any

from app.services.oauth_service import OAuthService
from app.core.oauth_providers import OAuthProvider, ZohoMultiDCConfig, ZohoDataCenter
from app.utils.redis_service import redis_service
from app.utils.secret_manager import secret_manager


class TestZohoMultiDC:
    """Test Zoho Multi-DC OAuth functionality."""

    @pytest.fixture
    def oauth_service(self):
        """Create OAuth service instance for testing."""
        return OAuthService(redis_service, secret_manager)

    @pytest.fixture
    def mock_provider_config(self):
        """Mock Zoho provider configuration."""
        config = Mock()
        config.client_id = "test-zoho-client-id"
        config.client_secret = "test-zoho-client-secret"
        config.redirect_uri = "https://test.com/callback"
        config.token_url = "https://accounts.zoho.com/oauth/v2/token"
        return config

    @pytest.fixture
    def mock_state_data(self):
        """Mock state data for OAuth flow."""
        return {
            "user_id": "test-user-123",
            "tool_name": "zoho",
            "provider": "zoho",
            "scopes": ["ZohoCRM.modules.ALL"],
            "redirect_uri": "https://test.com/callback",
            "custom_redirect_url": "https://app.test.com/success",
        }

    def test_zoho_dc_config(self):
        """Test Zoho data center configuration."""
        # Test US data center
        assert (
            ZohoMultiDCConfig.get_auth_url(ZohoDataCenter.US)
            == "https://accounts.zoho.com/oauth/v2/auth"
        )
        assert (
            ZohoMultiDCConfig.get_token_url(ZohoDataCenter.US)
            == "https://accounts.zoho.com/oauth/v2/token"
        )

        # Test India data center
        assert (
            ZohoMultiDCConfig.get_auth_url(ZohoDataCenter.IN)
            == "https://accounts.zoho.in/oauth/v2/auth"
        )
        assert (
            ZohoMultiDCConfig.get_token_url(ZohoDataCenter.IN)
            == "https://accounts.zoho.in/oauth/v2/token"
        )

        # Test EU data center
        assert (
            ZohoMultiDCConfig.get_auth_url(ZohoDataCenter.EU)
            == "https://accounts.zoho.eu/oauth/v2/auth"
        )
        assert (
            ZohoMultiDCConfig.get_token_url(ZohoDataCenter.EU)
            == "https://accounts.zoho.eu/oauth/v2/token"
        )

    def test_detect_dc_from_location(self):
        """Test data center detection from location parameter."""
        assert ZohoMultiDCConfig.detect_dc_from_location("in") == ZohoDataCenter.IN
        assert ZohoMultiDCConfig.detect_dc_from_location("eu") == ZohoDataCenter.EU
        assert ZohoMultiDCConfig.detect_dc_from_location("us") == ZohoDataCenter.US
        assert ZohoMultiDCConfig.detect_dc_from_location("unknown") == ZohoDataCenter.US  # Default

    @pytest.mark.asyncio
    async def test_zoho_us_user_success(self, oauth_service, mock_provider_config, mock_state_data):
        """Test successful token exchange for US data center user."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "access_token": "test-access-token",
            "refresh_token": "test-refresh-token",
            "api_domain": "https://www.zohoapis.com",
            "token_type": "Bearer",
            "expires_in": 3600,
        }

        with patch("httpx.AsyncClient") as mock_client:
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                return_value=mock_response
            )

            tokens = await oauth_service._handle_zoho_multidc_token_exchange(
                "test-auth-code", mock_provider_config, mock_state_data
            )

            assert tokens["access_token"] == "test-access-token"
            assert tokens["api_domain"] == "https://www.zohoapis.com"

    @pytest.mark.asyncio
    async def test_zoho_india_user_success(
        self, oauth_service, mock_provider_config, mock_state_data
    ):
        """Test token exchange for India data center user."""
        # US DC returns invalid_grant (user not found)
        us_response = Mock()
        us_response.status_code = 400
        us_response.headers = {"content-type": "application/json"}
        us_response.json.return_value = {
            "error": "invalid_grant",
            "error_description": "Invalid authorization code",
        }

        # India DC returns successful tokens
        india_response = Mock()
        india_response.status_code = 200
        india_response.json.return_value = {
            "access_token": "test-india-access-token",
            "refresh_token": "test-india-refresh-token",
            "api_domain": "https://www.zohoapis.in",
            "token_type": "Bearer",
            "expires_in": 3600,
        }

        with patch("httpx.AsyncClient") as mock_client:
            # Mock the post method to return different responses for different DCs
            mock_post = AsyncMock(side_effect=[us_response, india_response])
            mock_client.return_value.__aenter__.return_value.post = mock_post

            tokens = await oauth_service._handle_zoho_multidc_token_exchange(
                "test-auth-code", mock_provider_config, mock_state_data
            )

            # Verify we got the India DC tokens
            assert tokens["access_token"] == "test-india-access-token"
            assert tokens["api_domain"] == "https://www.zohoapis.in"

            # Verify two API calls were made (US failed, India succeeded)
            assert mock_post.call_count == 2

            # Verify first call was to US DC and second to India DC
            first_call_args = mock_post.call_args_list[0]
            second_call_args = mock_post.call_args_list[1]
            assert "accounts.zoho.com" in first_call_args[0][0]  # US URL
            assert "accounts.zoho.in" in second_call_args[0][0]  # India URL

    @pytest.mark.asyncio
    async def test_zoho_all_datacenters_fail(
        self, oauth_service, mock_provider_config, mock_state_data
    ):
        """Test error handling when all data centers fail."""
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.headers = {"content-type": "application/json"}
        mock_response.text = "Invalid authorization code"
        mock_response.json.return_value = {
            "error": "invalid_grant",
            "error_description": "Invalid authorization code",
        }

        with patch("httpx.AsyncClient") as mock_client:
            # All data centers return the same error
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                return_value=mock_response
            )

            with pytest.raises(
                RuntimeError, match="Zoho token exchange failed across all data centers"
            ):
                await oauth_service._handle_zoho_multidc_token_exchange(
                    "invalid-code", mock_provider_config, mock_state_data
                )

    @pytest.mark.asyncio
    async def test_zoho_network_error(self, oauth_service, mock_provider_config, mock_state_data):
        """Test network error handling in Zoho token exchange."""
        with patch("httpx.AsyncClient") as mock_client:
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                side_effect=httpx.RequestError("Network error")
            )

            with pytest.raises(RuntimeError, match="Zoho token exchange request failed"):
                await oauth_service._handle_zoho_multidc_token_exchange(
                    "test-code", mock_provider_config, mock_state_data
                )

    @pytest.mark.asyncio
    async def test_zoho_timeout_error(self, oauth_service, mock_provider_config, mock_state_data):
        """Test timeout error handling in Zoho token exchange."""
        with patch("httpx.AsyncClient") as mock_client:
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                side_effect=httpx.TimeoutException("Request timeout")
            )

            with pytest.raises(RuntimeError, match="Zoho token exchange request timed out"):
                await oauth_service._handle_zoho_multidc_token_exchange(
                    "test-code", mock_provider_config, mock_state_data
                )


if __name__ == "__main__":
    pytest.main([__file__])
