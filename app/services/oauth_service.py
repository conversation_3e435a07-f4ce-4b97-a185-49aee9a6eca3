"""
OAuth Service

This service provides OAuth functionality for multiple providers
with dynamic scope resolution and provider-specific handling.
"""

import json
import logging
import secrets
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urlencode

import httpx
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.oauth_providers import OAuthProvider, oauth_provider_manager
from app.models.oauth import OAuthCredential
from app.utils.redis_service import redis_service
from app.utils.secret_manager import secret_manager

logger = logging.getLogger(__name__)


class OAuthService:
    """Generalized OAuth service for multiple providers."""

    def __init__(self):
        """Initialize OAuth service with provider configurations."""
        self.secret_manager = secret_manager
        self.redis_service = redis_service
        self._initialize_providers()

        # Rate limiting configuration
        self.rate_limit_requests = 10  # Max requests per user per minute
        self.rate_limit_window = 60  # Window in seconds

    def _initialize_providers(self):
        """Initialize OAuth provider configurations from settings."""

        # Google OAuth
        if settings.GOOGLE_CLIENT_ID and settings.GOOGLE_CLIENT_SECRET:
            oauth_provider_manager.update_provider_credentials(
                OAuthProvider.GOOGLE,
                settings.GOOGLE_CLIENT_ID,
                settings.GOOGLE_CLIENT_SECRET.get_secret_value(),
                settings.GOOGLE_REDIRECT_URI or "",
            )

        # Microsoft OAuth
        if settings.MICROSOFT_CLIENT_ID and settings.MICROSOFT_CLIENT_SECRET:
            oauth_provider_manager.update_provider_credentials(
                OAuthProvider.MICROSOFT,
                settings.MICROSOFT_CLIENT_ID,
                settings.MICROSOFT_CLIENT_SECRET.get_secret_value(),
                settings.MICROSOFT_REDIRECT_URI or "",
            )

        # GitHub OAuth
        if settings.GITHUB_CLIENT_ID and settings.GITHUB_CLIENT_SECRET:
            oauth_provider_manager.update_provider_credentials(
                OAuthProvider.GITHUB,
                settings.GITHUB_CLIENT_ID,
                settings.GITHUB_CLIENT_SECRET.get_secret_value(),
                settings.GITHUB_REDIRECT_URI or "",
            )

        # Slack OAuth
        if settings.SLACK_CLIENT_ID and settings.SLACK_CLIENT_SECRET:
            oauth_provider_manager.update_provider_credentials(
                OAuthProvider.SLACK,
                settings.SLACK_CLIENT_ID,
                settings.SLACK_CLIENT_SECRET.get_secret_value(),
                settings.SLACK_REDIRECT_URI or "",
            )

        # Jira OAuth
        if settings.JIRA_CLIENT_ID and settings.JIRA_CLIENT_SECRET:
            oauth_provider_manager.update_provider_credentials(
                OAuthProvider.JIRA,
                settings.JIRA_CLIENT_ID,
                settings.JIRA_CLIENT_SECRET.get_secret_value(),
                settings.JIRA_REDIRECT_URI or "",
            )

        # Zoho OAuth
        if settings.ZOHO_CLIENT_ID and settings.ZOHO_CLIENT_SECRET:
            oauth_provider_manager.update_provider_credentials(
                OAuthProvider.ZOHO,
                settings.ZOHO_CLIENT_ID,
                settings.ZOHO_CLIENT_SECRET.get_secret_value(),
                settings.ZOHO_REDIRECT_URI or "",
            )

        # Load custom providers
        if settings.CUSTOM_OAUTH_PROVIDERS:
            oauth_provider_manager.load_custom_providers_from_json(settings.CUSTOM_OAUTH_PROVIDERS)

    def _check_rate_limit(self, user_id: str) -> bool:
        """
        Check if user has exceeded OAuth initiation rate limit.

        Args:
            user_id: User ID to check

        Returns:
            True if within rate limit, False if exceeded
        """
        rate_limit_key = f"oauth_rate_limit:{user_id}"

        # Get current request count
        current_count = self.redis_service.get_data_from_redis(rate_limit_key)

        if current_count is None:
            # First request in window
            self.redis_service.set_data_in_redis_with_ttl(
                rate_limit_key, self.rate_limit_window, "1"
            )
            return True

        try:
            count = int(current_count)
            if count >= self.rate_limit_requests:
                logger.warning(f"Rate limit exceeded for user {user_id}: {count} requests")
                return False

            # Increment counter (Redis INCR would be better but using current interface)
            self.redis_service.set_data_in_redis_with_ttl(
                rate_limit_key, self.rate_limit_window, str(count + 1)
            )
            return True

        except ValueError:
            # Invalid count, reset
            self.redis_service.set_data_in_redis_with_ttl(
                rate_limit_key, self.rate_limit_window, "1"
            )
            return True

    def _validate_oauth_parameters(
        self, user_id: str, tool_name: str, provider: OAuthProvider
    ) -> None:
        """
        Validate OAuth request parameters.

        Args:
            user_id: User ID
            tool_name: Tool name
            provider: OAuth provider

        Raises:
            ValueError: If any parameter is invalid
        """
        if not user_id or not user_id.strip():
            raise ValueError("User ID is required")

        if not tool_name or not tool_name.strip():
            raise ValueError("Tool name is required")

        if not isinstance(provider, OAuthProvider):
            raise ValueError("Invalid OAuth provider")

        # Validate provider is configured
        if not oauth_provider_manager.is_provider_configured(provider):
            raise ValueError(f"Provider not configured: {provider.value}")

        # Validate tool has scopes for provider
        scopes = oauth_provider_manager.get_tool_scopes(tool_name, provider)
        if not scopes:
            raise ValueError(
                f"No scopes configured for tool {tool_name} and provider {provider.value}"
            )

    def generate_authorization_url(
        self,
        user_id: str,
        tool_name: str,
        provider: OAuthProvider,
        custom_scopes: Optional[List[str]] = None,
        custom_redirect_uri: Optional[str] = None,
    ) -> Tuple[str, str]:
        """
        Generate OAuth authorization URL and state token with rate limiting and validation.

        Args:
            user_id: User ID
            tool_name: Tool name
            provider: OAuth provider
            custom_scopes: Custom scopes (optional)
            custom_redirect_uri: Custom redirect URI (optional)

        Returns:
            Tuple of (authorization_url, state_token)

        Raises:
            ValueError: If parameters are invalid or rate limit exceeded
            RuntimeError: If state storage fails
        """
        # Validate parameters
        self._validate_oauth_parameters(user_id, tool_name, provider)

        # Check rate limiting
        if not self._check_rate_limit(user_id):
            raise ValueError(
                f"Rate limit exceeded for user {user_id}. Max {self.rate_limit_requests} requests per minute."
            )

        # Get provider configuration
        provider_config = oauth_provider_manager.get_provider_config(provider)
        if not provider_config:
            raise ValueError(f"Provider not configured: {provider.value}")

        # Get scopes for the tool
        scopes = custom_scopes or oauth_provider_manager.get_tool_scopes(tool_name, provider)
        if not scopes:
            raise ValueError(
                f"No scopes configured for tool {tool_name} and provider {provider.value}"
            )

        # Generate cryptographically secure state token (32+ bytes)
        state_token = secrets.token_urlsafe(32)

        # Always use the configured provider redirect URI for OAuth
        oauth_redirect_uri = provider_config.redirect_uri

        # Handle Slack's dual-scope system
        slack_scopes = None
        if oauth_provider_manager.is_slack_provider(provider):
            slack_scopes = oauth_provider_manager.get_slack_scopes_separated(tool_name)
            logger.info(
                f"Slack scopes separated - Bot: {slack_scopes['bot_scopes']}, User: {slack_scopes['user_scopes']}"
            )

        # Store state data in Redis with comprehensive information
        state_data = {
            "user_id": user_id,
            "tool_name": tool_name,
            "provider": provider.value,
            "scopes": scopes,
            "slack_scopes": slack_scopes,  # Store separated scopes for Slack
            "redirect_uri": oauth_redirect_uri,  # This is for OAuth provider callback
            "custom_redirect_url": custom_redirect_uri,  # This is for final user redirect
            "created_at": datetime.now(timezone.utc).isoformat(),
        }

        redis_key = f"oauth_state:{state_token}"
        success = self.redis_service.set_data_in_redis_with_ttl(
            hash_key=redis_key, ttl=settings.OAUTH_STATE_EXPIRY_SECONDS, data=state_data
        )

        if not success:
            raise RuntimeError("Failed to store OAuth state in Redis")

        # Build authorization URL parameters
        auth_params = {
            "client_id": provider_config.client_id,
            "redirect_uri": oauth_redirect_uri,
            "state": state_token,
            "response_type": provider_config.response_type,
        }

        # Handle Slack's dual-scope system
        if oauth_provider_manager.is_slack_provider(provider) and slack_scopes:
            # For Slack, use separate scope and user_scope parameters
            if slack_scopes["bot_scopes"]:
                auth_params["scope"] = ",".join(slack_scopes["bot_scopes"])
            if slack_scopes["user_scopes"]:
                auth_params["user_scope"] = ",".join(slack_scopes["user_scopes"])
        else:
            # For other providers, use standard scope parameter
            auth_params["scope"] = " ".join(scopes)

        # Add provider-specific parameters
        if provider_config.access_type:
            auth_params["access_type"] = provider_config.access_type

        if provider_config.prompt:
            auth_params["prompt"] = provider_config.prompt

        # Add extra parameters
        auth_params.update(provider_config.extra_auth_params)

        # Build authorization URL
        authorization_url = f"{provider_config.auth_url}?{urlencode(auth_params)}"

        logger.info(
            f"Generated OAuth authorization URL for {provider.value} - User: {user_id}, Tool: {tool_name}"
        )
        logger.info(f"Authorization URL: {authorization_url}")

        return authorization_url, state_token

    async def exchange_code_for_tokens(
        self, code: str, state: str
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Exchange authorization code for OAuth tokens with enhanced validation.

        Args:
            code: Authorization code from OAuth provider
            state: State token from OAuth provider

        Returns:
            Tuple of (tokens, state_data)

        Raises:
            ValueError: If state is invalid, expired, or code exchange fails
            RuntimeError: If token exchange request fails
        """
        # Validate input parameters
        if not code or not code.strip():
            raise ValueError("Authorization code is required")

        if not state or not state.strip():
            raise ValueError("State parameter is required")

        # Retrieve and validate state data using constant-time comparison
        redis_key = f"oauth_state:{state}"
        state_data = self.redis_service.get_json_data_from_redis(redis_key)

        if not state_data:
            logger.warning(f"Invalid or expired OAuth state: {state[:8]}...")
            raise ValueError("Invalid or expired OAuth state")

        # Validate state data structure
        required_fields = ["user_id", "tool_name", "provider", "scopes", "created_at"]
        for field in required_fields:
            if field not in state_data:
                logger.error(f"Missing required field in state data: {field}")
                raise ValueError("Invalid state data structure")

        # Clean up state from Redis immediately after retrieval
        self.redis_service.delete_data_from_redis(redis_key)

        # Get provider configuration
        provider = OAuthProvider(state_data["provider"])
        provider_config = oauth_provider_manager.get_provider_config(provider)

        if not provider_config:
            raise ValueError(f"Provider not configured: {provider.value}")

        # Prepare token exchange request
        token_data = {
            "client_id": provider_config.client_id,
            "client_secret": provider_config.client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": provider_config.redirect_uri,
        }

        # Exchange code for tokens with comprehensive error handling
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {
                    "Accept": "application/json",
                    "Content-Type": "application/x-www-form-urlencoded",
                }

                response = await client.post(
                    provider_config.token_url, data=token_data, headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(
                        f"Token exchange failed for provider {provider.value}: {response.status_code} - {error_text}"
                    )

                    # Try to parse error response
                    try:
                        error_data = response.json()
                        error_description = error_data.get(
                            "error_description", error_data.get("error", "Unknown error")
                        )
                        raise RuntimeError(f"OAuth token exchange failed: {error_description}")
                    except (ValueError, KeyError):
                        raise RuntimeError(
                            f"OAuth token exchange failed with status {response.status_code}"
                        )

                tokens = response.json()

                # Validate required token fields
                if "access_token" not in tokens:
                    logger.error(f"Missing access_token in response from {provider.value}")
                    raise RuntimeError("Invalid token response: missing access_token")

        except httpx.TimeoutException:
            logger.error(f"Timeout during token exchange with {provider.value}")
            raise RuntimeError("Token exchange request timed out")
        except httpx.RequestError as e:
            logger.error(f"Request error during token exchange with {provider.value}: {e}")
            raise RuntimeError(f"Token exchange request failed: {str(e)}")

        logger.info(
            f"Successfully exchanged code for tokens - Provider: {provider.value}, User: {state_data['user_id']}"
        )

        return tokens, state_data

    def store_oauth_credentials(
        self,
        db: Session,
        user_id: str,
        tool_name: str,
        provider: OAuthProvider,
        tokens: Dict[str, Any],
        scopes: List[str],
    ) -> Dict[str, Any]:
        """
        Store OAuth credentials in Secret Manager and database.

        Args:
            db: Database session
            user_id: User ID
            tool_name: Tool name
            provider: OAuth provider
            tokens: Token dictionary from OAuth exchange
            scopes: Granted scopes

        Returns:
            Dictionary with operation status
        """
        try:
            # Create composite key (includes provider)
            composite_key = OAuthCredential.generate_composite_key(
                user_id, tool_name, provider.value
            )

            # Check if credential already exists
            existing_credential = (
                db.query(OAuthCredential)
                .filter(OAuthCredential.composite_key == composite_key)
                .first()
            )

            # Store tokens in Secret Manager
            secret_id = self.secret_manager.store_oauth_tokens(
                user_id=user_id, tool_name=tool_name, tokens=tokens
            )

            if existing_credential:
                # Update existing credential
                existing_credential.secret_reference = secret_id
                existing_credential.scopes = json.dumps(scopes)
                existing_credential.updated_at = datetime.now(timezone.utc)
                existing_credential.last_used_at = datetime.now(timezone.utc)

                logger.info(f"Updated OAuth credential for composite key: {composite_key}")
            else:
                # Create new credential
                new_credential = OAuthCredential(
                    id=str(uuid.uuid4()),
                    user_id=user_id,
                    tool_name=tool_name,
                    provider=provider.value,
                    composite_key=composite_key,
                    secret_reference=secret_id,
                    scopes=json.dumps(scopes),
                )

                db.add(new_credential)
                logger.info(f"Created new OAuth credential for composite key: {composite_key}")

            db.commit()

            return {
                "success": True,
                "message": "OAuth credentials stored successfully",
                "composite_key": composite_key,
            }

        except IntegrityError as e:
            db.rollback()
            logger.error(f"Database integrity error storing OAuth credentials: {e}")
            return {"success": False, "message": "Database integrity error", "error": str(e)}
        except Exception as e:
            db.rollback()
            logger.error(f"Error storing OAuth credentials: {e}")
            return {
                "success": False,
                "message": "Failed to store OAuth credentials",
                "error": str(e),
            }

    def retrieve_oauth_credentials(
        self, db: Session, user_id: str, tool_name: str, provider: OAuthProvider
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve OAuth credentials from database and Secret Manager.

        Args:
            db: Database session
            user_id: User ID
            tool_name: Tool name
            provider: OAuth provider

        Returns:
            OAuth credentials dictionary if found, None otherwise
        """
        try:
            # Create composite key
            composite_key = OAuthCredential.generate_composite_key(
                user_id, tool_name, provider.value
            )

            # Find credential in database
            credential = (
                db.query(OAuthCredential)
                .filter(OAuthCredential.composite_key == composite_key)
                .first()
            )

            if not credential:
                logger.warning(f"No OAuth credential found for composite key: {composite_key}")
                return None

            # Retrieve tokens from Secret Manager
            tokens = self.secret_manager.retrieve_oauth_tokens(credential.secret_reference)

            if not tokens:
                logger.error(
                    f"Failed to retrieve tokens from Secret Manager: {credential.secret_reference}"
                )
                return None

            # Update last used timestamp
            credential.update_last_used()
            db.commit()

            # Parse scopes
            scopes = json.loads(credential.scopes) if credential.scopes else []

            logger.info(f"Retrieved OAuth credentials for composite key: {composite_key}")

            # Build base response
            response = {
                "user_id": user_id,
                "tool_name": tool_name,
                "provider": provider.value,
                "access_token": tokens.get("access_token"),
                "refresh_token": tokens.get("refresh_token"),
                "token_type": tokens.get("token_type", "Bearer"),
                "expires_in": tokens.get("expires_in"),
                "scope": " ".join(scopes),
                "scopes": scopes,
                "tokens": tokens,
            }

            # Add Slack-specific token fields if this is a Slack provider
            if oauth_provider_manager.is_slack_provider(provider):
                response = {
                    "user_id": user_id,
                    "tool_name": tool_name,
                    "provider": provider.value,
                    "access_token": "",
                    "refresh_token": "",
                    "token_type": "Bearer",
                    "expires_in": 0,
                    "scope": tokens.get("scope"),
                    "scopes": scopes,
                }
                # Bot token (main access_token)
                response["bot_token"] = tokens.get("access_token")
                response["bot_user_id"] = tokens.get("bot_user_id")

                # User token from authed_user
                authed_user = tokens.get("authed_user", {})
                if authed_user and authed_user.get("access_token"):
                    response["user_token"] = authed_user.get("access_token")
                    response["user_id_slack"] = authed_user.get("id")
                    response["user_scope"] = authed_user.get("scope", "")

                # Team/workspace information
                team = tokens.get("team", {})
                if team:
                    response["team_id"] = team.get("id")
                    response["team_name"] = team.get("name")

            return response

        except Exception as e:
            logger.error(f"Error retrieving OAuth credentials: {e}")
            return None

    def delete_oauth_credentials(
        self, db: Session, user_id: str, tool_name: str, provider: OAuthProvider
    ) -> bool:
        """
        Delete OAuth credentials from database and Secret Manager.

        Args:
            db: Database session
            user_id: User ID
            tool_name: Tool name
            provider: OAuth provider

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create composite key
            composite_key = OAuthCredential.generate_composite_key(
                user_id, tool_name, provider.value
            )

            # Find credential in database
            credential = (
                db.query(OAuthCredential)
                .filter(OAuthCredential.composite_key == composite_key)
                .first()
            )

            if not credential:
                logger.warning(f"No OAuth credential found for deletion: {composite_key}")
                return True  # Consider it successful if already deleted

            # Delete from Secret Manager
            secret_deleted = self.secret_manager.delete_oauth_tokens(credential.secret_reference)

            # Delete from database
            db.delete(credential)
            db.commit()

            logger.info(f"Deleted OAuth credential: {composite_key}")

            return secret_deleted

        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting OAuth credentials: {e}")
            return False

    async def refresh_access_token(
        self, db: Session, user_id: str, tool_name: str, provider: OAuthProvider
    ) -> Optional[Dict[str, Any]]:
        """
        Refresh OAuth access token using refresh token.

        Args:
            db: Database session
            user_id: User ID
            tool_name: Tool name
            provider: OAuth provider

        Returns:
            Updated credentials if successful, None otherwise
        """
        try:
            # Get current credentials
            credentials = self.retrieve_oauth_credentials(db, user_id, tool_name, provider)

            if not credentials or not credentials.get("tokens", {}).get("refresh_token"):
                logger.warning(f"No refresh token available for {user_id}/{tool_name}")
                return None

            # Get provider configuration
            provider_config = oauth_provider_manager.get_provider_config(provider)
            if not provider_config:
                raise ValueError(f"Provider not configured: {provider.value}")

            # Prepare refresh token request
            refresh_data = {
                "client_id": provider_config.client_id,
                "client_secret": provider_config.client_secret,
                "refresh_token": credentials["tokens"]["refresh_token"],
                "grant_type": "refresh_token",
            }

            # Request new access token
            async with httpx.AsyncClient() as client:
                headers = {"Accept": "application/json"}

                response = await client.post(
                    provider_config.token_url, data=refresh_data, headers=headers, timeout=30.0
                )

                if response.status_code != 200:
                    logger.error(f"Token refresh failed: {response.status_code} - {response.text}")
                    return None

                new_tokens = response.json()

            # Update tokens (preserve refresh token if not provided)
            updated_tokens = credentials["tokens"].copy()
            updated_tokens.update(new_tokens)

            if "refresh_token" not in new_tokens and "refresh_token" in credentials["tokens"]:
                updated_tokens["refresh_token"] = credentials["tokens"]["refresh_token"]

            # Store updated tokens
            result = self.store_oauth_credentials(
                db, user_id, tool_name, provider, updated_tokens, credentials["scopes"]
            )

            if result["success"]:
                logger.info(f"Refreshed OAuth token for {user_id}/{tool_name}")

                # Return updated credentials
                return self.retrieve_oauth_credentials(db, user_id, tool_name, provider)
            else:
                logger.error(f"Failed to store refreshed tokens: {result['message']}")
                return None

        except Exception as e:
            logger.error(f"Error refreshing OAuth token: {e}")
            return None


# Global OAuth service instance
oauth_service = OAuthService()
