"""
gRPC Authentication Service Implementation

This module implements the gRPC service for OAuth authentication
and credential management.
"""

import asyncio
import logging

import grpc
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.oauth_providers import OAuthProvider, oauth_provider_manager
from app.db.session import get_db
from app.grpc_ import authentication_pb2, authentication_pb2_grpc
from app.services.oauth_service import oauth_service
from app.utils.redis_service import redis_service
from app.utils.secret_manager import secret_manager

logger = logging.getLogger(__name__)


class AuthenticationServicer(authentication_pb2_grpc.AuthenticationServiceServicer):
    """gRPC Authentication Service implementation."""

    def __init__(self):
        """Initialize the authentication servicer."""
        self.oauth_service = oauth_service

    def _get_db_session(self) -> Session:
        """Get database session."""
        return next(get_db())

    def _validate_server_auth_key(self, server_auth_key: str) -> bool:
        """Validate server authentication key."""
        expected_key = settings.SERVER_AUTH_KEY.get_secret_value()
        return server_auth_key == expected_key

    def InitiateOAuth(
        self, request: authentication_pb2.OAuthAuthorizeRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.OAuthAuthorizeResponse:
        """Initiate OAuth authorization flow."""
        try:
            # Convert provider enum to OAuthProvider
            provider_map = {
                authentication_pb2.OAUTH_PROVIDER_GOOGLE: OAuthProvider.GOOGLE,
                authentication_pb2.OAUTH_PROVIDER_MICROSOFT: OAuthProvider.MICROSOFT,
                authentication_pb2.OAUTH_PROVIDER_GITHUB: OAuthProvider.GITHUB,
                authentication_pb2.OAUTH_PROVIDER_SLACK: OAuthProvider.SLACK,
                authentication_pb2.OAUTH_PROVIDER_CUSTOM: OAuthProvider.CUSTOM,
                authentication_pb2.OAUTH_PROVIDER_JIRA: OAuthProvider.JIRA,
                authentication_pb2.OAUTH_PROVIDER_ZOHO: OAuthProvider.ZOHO,
            }

            provider = provider_map.get(request.provider)
            if not provider:
                return authentication_pb2.OAuthAuthorizeResponse(
                    success=False, message="Invalid OAuth provider specified"
                )

            # Generate authorization URL
            auth_url, state_token = self.oauth_service.generate_authorization_url(
                user_id=request.user_id,
                tool_name=request.tool_name,
                provider=provider,
                custom_scopes=list(request.scopes) if request.scopes else None,
                custom_redirect_uri=request.redirect_uri if request.redirect_uri else None,
            )

            return authentication_pb2.OAuthAuthorizeResponse(
                success=True,
                message="OAuth authorization URL generated successfully",
                authorization_url=auth_url,
                state=state_token,
            )

        except ValueError as e:
            logger.error(f"OAuth initiation error: {e}")
            return authentication_pb2.OAuthAuthorizeResponse(success=False, message=str(e))
        except Exception as e:
            logger.error(f"Unexpected error in OAuth initiation: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.OAuthAuthorizeResponse(
                success=False, message="Internal server error"
            )

    def HandleOAuthCallback(
        self, request: authentication_pb2.OAuthCallbackRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.OAuthCallbackResponse:
        """Handle OAuth callback and exchange code for tokens."""
        try:
            # Try to get state_data for redirect_url even in error cases
            redirect_url = ""
            if request.state:
                try:
                    # Get state data from Redis directly
                    from app.utils.redis_service import redis_service

                    redis_key = f"oauth_state:{request.state}"
                    state_data = redis_service.get_json_data_from_redis(redis_key)
                    if state_data:
                        redirect_url = state_data.get("custom_redirect_url", "")
                except Exception:
                    # If we can't get state_data, continue without redirect_url
                    pass

            # Handle OAuth errors
            if request.error:
                logger.error(f"OAuth error received in callback: {request.error}")
                return authentication_pb2.OAuthCallbackResponse(
                    success=False,
                    message=f"Authorization failed: {request.error}",
                    redirect_url=redirect_url,
                )

            if not request.code:
                return authentication_pb2.OAuthCallbackResponse(
                    success=False,
                    message="Authorization code not provided",
                    redirect_url=redirect_url,
                )

            # Exchange code for tokens
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                tokens, state_data = loop.run_until_complete(
                    self.oauth_service.exchange_code_for_tokens(request.code, request.state)
                )
            finally:
                loop.close()

            # Store credentials
            db = self._get_db_session()
            try:
                provider = OAuthProvider(state_data["provider"])

                result = self.oauth_service.store_oauth_credentials(
                    db=db,
                    user_id=state_data["user_id"],
                    tool_name=state_data["tool_name"],
                    provider=provider,
                    tokens=tokens,
                    scopes=state_data["scopes"],
                )

                if result["success"]:
                    return authentication_pb2.OAuthCallbackResponse(
                        success=True,
                        message="OAuth authorization completed successfully",
                        user_id=state_data["user_id"],
                        tool_name=state_data["tool_name"],
                        provider=state_data["provider"],
                        redirect_url=state_data.get("custom_redirect_url", ""),
                    )
                else:
                    return authentication_pb2.OAuthCallbackResponse(
                        success=False,
                        message=f"Failed to store credentials: {result['message']}",
                        redirect_url=state_data.get("custom_redirect_url", ""),
                    )

            finally:
                db.close()

        except ValueError as e:
            logger.error(f"OAuth callback error: {e}")
            return authentication_pb2.OAuthCallbackResponse(
                success=False, message=str(e), redirect_url=""
            )
        except Exception as e:
            logger.error(f"Unexpected error in OAuth callback: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.OAuthCallbackResponse(
                success=False, message="Internal server error", redirect_url=""
            )

    # TODO: Add RefreshOAuthTokens method once gRPC messages are properly generated
    # def RefreshOAuthTokens(self, request, context):
    #     """Refresh OAuth access tokens using refresh token."""
    #     context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    #     context.set_details('Method not implemented!')
    #     raise NotImplementedError('Method not implemented!')

    def GetOAuthCredentials(
        self, request: authentication_pb2.OAuthCredentialRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.OAuthCredentialResponse:
        """Retrieve OAuth credentials for a user."""
        try:
            # Convert provider enum
            provider_map = {
                authentication_pb2.OAUTH_PROVIDER_GOOGLE: OAuthProvider.GOOGLE,
                authentication_pb2.OAUTH_PROVIDER_MICROSOFT: OAuthProvider.MICROSOFT,
                authentication_pb2.OAUTH_PROVIDER_GITHUB: OAuthProvider.GITHUB,
                authentication_pb2.OAUTH_PROVIDER_SLACK: OAuthProvider.SLACK,
                authentication_pb2.OAUTH_PROVIDER_CUSTOM: OAuthProvider.CUSTOM,
                authentication_pb2.OAUTH_PROVIDER_JIRA: OAuthProvider.JIRA,
                authentication_pb2.OAUTH_PROVIDER_ZOHO: OAuthProvider.ZOHO,
            }

            provider = provider_map.get(request.provider, OAuthProvider.GOOGLE)

            # Retrieve credentials
            db = self._get_db_session()
            try:
                credentials = self.oauth_service.retrieve_oauth_credentials(
                    db=db,
                    user_id=request.user_id,
                    tool_name=request.tool_name,
                    provider=provider,
                )

                if credentials:
                    if oauth_provider_manager.is_slack_provider(provider):
                        return authentication_pb2.OAuthCredentialResponse(
                            success=True,
                            message="OAuth credentials retrieved successfully",
                            **credentials,
                        )
                    else:
                        return authentication_pb2.OAuthCredentialResponse(
                            success=True,
                            message="OAuth credentials retrieved successfully",
                            user_id=credentials["user_id"],
                            tool_name=credentials["tool_name"],
                            provider=credentials["provider"],
                            access_token=credentials["access_token"] or "",
                            refresh_token=credentials.get("refresh_token") or "",
                            token_type=credentials["token_type"] or "Bearer",
                            expires_in=credentials["expires_in"],
                            scope=credentials["scope"] or "",
                        )
                else:
                    return authentication_pb2.OAuthCredentialResponse(
                        success=False, message="OAuth credentials not found"
                    )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error retrieving OAuth credentials: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.OAuthCredentialResponse(
                success=False, message="Internal server error"
            )

    def GetServerOAuthCredentials(
        self,
        request: authentication_pb2.ServerOAuthCredentialRequest,
        context: grpc.ServicerContext,
    ) -> authentication_pb2.OAuthCredentialResponse:
        """Retrieve OAuth credentials for server-to-server access."""
        try:
            # Validate server authentication key
            if not self._validate_server_auth_key(request.server_auth_key):
                context.set_code(grpc.StatusCode.UNAUTHENTICATED)
                context.set_details("Invalid server authentication key")
                return authentication_pb2.OAuthCredentialResponse(
                    success=False, message="Authentication failed"
                )

            # Convert provider enum
            provider_map = {
                authentication_pb2.OAUTH_PROVIDER_GOOGLE: OAuthProvider.GOOGLE,
                authentication_pb2.OAUTH_PROVIDER_MICROSOFT: OAuthProvider.MICROSOFT,
                authentication_pb2.OAUTH_PROVIDER_GITHUB: OAuthProvider.GITHUB,
                authentication_pb2.OAUTH_PROVIDER_SLACK: OAuthProvider.SLACK,
                authentication_pb2.OAUTH_PROVIDER_CUSTOM: OAuthProvider.CUSTOM,
                authentication_pb2.OAUTH_PROVIDER_JIRA: OAuthProvider.JIRA,
                authentication_pb2.OAUTH_PROVIDER_ZOHO: OAuthProvider.ZOHO,
            }

            provider = provider_map.get(request.provider, OAuthProvider.GOOGLE)

            # Retrieve credentials
            db = self._get_db_session()
            try:
                credentials = self.oauth_service.retrieve_oauth_credentials(
                    db=db,
                    user_id=request.user_id,
                    tool_name=request.tool_name,
                    provider=provider,
                )

                if credentials:
                    if oauth_provider_manager.is_slack_provider(provider):
                        return authentication_pb2.OAuthCredentialResponse(
                            success=True,
                            message="OAuth credentials retrieved successfully",
                            **credentials,
                        )
                    else:
                        return authentication_pb2.OAuthCredentialResponse(
                            success=True,
                            message="OAuth credentials retrieved successfully",
                            user_id=credentials["user_id"],
                            tool_name=credentials["tool_name"],
                            provider=credentials["provider"],
                            access_token=credentials["access_token"] or "",
                            refresh_token=credentials.get("refresh_token") or "",
                            token_type=credentials["token_type"] or "Bearer",
                            expires_in=credentials["expires_in"],
                            scope=credentials["scope"] or "",
                        )
                else:
                    return authentication_pb2.OAuthCredentialResponse(
                        success=False, message="OAuth credentials not found"
                    )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error retrieving server OAuth credentials: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.OAuthCredentialResponse(
                success=False, message="Internal server error"
            )

    def DeleteOAuthCredentials(
        self,
        request: authentication_pb2.DeleteOAuthCredentialRequest,
        context: grpc.ServicerContext,
    ) -> authentication_pb2.DeleteOAuthCredentialResponse:
        """Delete OAuth credentials."""
        try:
            # Convert provider enum
            provider_map = {
                authentication_pb2.OAUTH_PROVIDER_GOOGLE: OAuthProvider.GOOGLE,
                authentication_pb2.OAUTH_PROVIDER_MICROSOFT: OAuthProvider.MICROSOFT,
                authentication_pb2.OAUTH_PROVIDER_GITHUB: OAuthProvider.GITHUB,
                authentication_pb2.OAUTH_PROVIDER_SLACK: OAuthProvider.SLACK,
                authentication_pb2.OAUTH_PROVIDER_CUSTOM: OAuthProvider.CUSTOM,
                authentication_pb2.OAUTH_PROVIDER_JIRA: OAuthProvider.JIRA,
                authentication_pb2.OAUTH_PROVIDER_ZOHO: OAuthProvider.ZOHO,
            }

            provider = provider_map.get(request.provider, OAuthProvider.GOOGLE)

            # Delete credentials
            db = self._get_db_session()
            try:
                success = self.oauth_service.delete_oauth_credentials(
                    db=db,
                    user_id=request.user_id,
                    tool_name=request.tool_name,
                    provider=provider,
                )

                if success:
                    return authentication_pb2.DeleteOAuthCredentialResponse(
                        success=True, message="OAuth credentials deleted successfully"
                    )
                else:
                    return authentication_pb2.DeleteOAuthCredentialResponse(
                        success=False, message="Failed to delete OAuth credentials"
                    )

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error deleting OAuth credentials: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.DeleteOAuthCredentialResponse(
                success=False, message="Internal server error"
            )

    def ListOAuthProviders(
        self, request: authentication_pb2.OAuthProvidersListRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.OAuthProvidersListResponse:
        """List available OAuth providers."""
        try:
            providers = []
            all_providers = oauth_provider_manager.get_all_providers()

            for provider, config in all_providers.items():
                # Get supported tools for this provider
                all_tool_scopes = oauth_provider_manager.get_all_tool_scopes()
                supported_tools = [
                    tool_name
                    for tool_name, mapping in all_tool_scopes.items()
                    if provider in mapping.provider_scopes
                ]

                provider_info = authentication_pb2.OAuthProviderInfo(
                    provider=getattr(
                        authentication_pb2, f"OAUTH_PROVIDER_{provider.value.upper()}"
                    ),
                    name=provider.value,
                    display_name=config.provider.value.title(),
                    supported_tools=supported_tools,
                    is_configured=oauth_provider_manager.is_provider_configured(provider),
                )
                providers.append(provider_info)

            return authentication_pb2.OAuthProvidersListResponse(
                success=True, message="OAuth providers retrieved successfully", providers=providers
            )

        except Exception as e:
            logger.error(f"Error listing OAuth providers: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.OAuthProvidersListResponse(
                success=False, message="Internal server error", providers=[]
            )

    def GetToolScopes(
        self, request: authentication_pb2.OAuthToolScopesRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.OAuthToolScopesResponse:
        """Get required scopes for a tool and provider."""
        try:
            # Convert provider enum
            provider_map = {
                authentication_pb2.OAUTH_PROVIDER_GOOGLE: OAuthProvider.GOOGLE,
                authentication_pb2.OAUTH_PROVIDER_MICROSOFT: OAuthProvider.MICROSOFT,
                authentication_pb2.OAUTH_PROVIDER_GITHUB: OAuthProvider.GITHUB,
                authentication_pb2.OAUTH_PROVIDER_SLACK: OAuthProvider.SLACK,
                authentication_pb2.OAUTH_PROVIDER_CUSTOM: OAuthProvider.CUSTOM,
                authentication_pb2.OAUTH_PROVIDER_JIRA: OAuthProvider.JIRA,
                authentication_pb2.OAUTH_PROVIDER_ZOHO: OAuthProvider.ZOHO,
            }

            provider = provider_map.get(request.provider, OAuthProvider.GOOGLE)

            # Get scopes for the tool
            scopes = oauth_provider_manager.get_tool_scopes(request.tool_name, provider)

            # Get tool mapping for description
            all_tool_scopes = oauth_provider_manager.get_all_tool_scopes()
            tool_mapping = all_tool_scopes.get(request.tool_name)
            description = tool_mapping.description if tool_mapping else ""

            return authentication_pb2.OAuthToolScopesResponse(
                success=True,
                message="Tool scopes retrieved successfully",
                tool_name=request.tool_name,
                provider=getattr(authentication_pb2, f"OAUTH_PROVIDER_{provider.value.upper()}"),
                scopes=scopes,
                description=description,
            )

        except Exception as e:
            logger.error(f"Error getting tool scopes: {e}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return authentication_pb2.OAuthToolScopesResponse(
                success=False,
                message="Internal server error",
                tool_name=request.tool_name,
                provider=request.provider,
                scopes=[],
                description="",
            )

    def HealthCheck(
        self, request: authentication_pb2.HealthCheckRequest, context: grpc.ServicerContext
    ) -> authentication_pb2.HealthCheckResponse:
        """Perform health check."""
        try:
            dependencies = {}

            # Check Redis
            try:
                redis_healthy = redis_service.health_check()
                dependencies["redis"] = "healthy" if redis_healthy else "unhealthy"
            except Exception:
                dependencies["redis"] = "unhealthy"

            # Check Secret Manager
            try:
                sm_healthy = secret_manager.health_check()
                dependencies["secret_manager"] = "healthy" if sm_healthy else "unhealthy"
            except Exception:
                dependencies["secret_manager"] = "unhealthy"

            # Check database
            try:
                db = self._get_db_session()
                db.execute("SELECT 1")
                db.close()
                dependencies["database"] = "healthy"
            except Exception:
                dependencies["database"] = "unhealthy"

            # Overall health
            all_healthy = all(status == "healthy" for status in dependencies.values())

            return authentication_pb2.HealthCheckResponse(
                healthy=all_healthy,
                status="healthy" if all_healthy else "degraded",
                version=settings.APP_VERSION,
                dependencies=dependencies,
            )

        except Exception as e:
            logger.error(f"Error in health check: {e}")
            return authentication_pb2.HealthCheckResponse(
                healthy=False,
                status="unhealthy",
                version=settings.APP_VERSION,
                dependencies={"error": str(e)},
            )
