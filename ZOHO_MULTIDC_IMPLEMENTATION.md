# Zoho Multi-DC OAuth Implementation

## Problem Statement

The original Zoho OAuth implementation was hardcoded to use the US data center endpoints (`https://accounts.zoho.com`). This caused authentication failures for users from other data centers (India, EU, Australia, etc.) with the error:

```
{"success":false,"message":"Authentication service error: Invalid token response: missing access_token"}
```

## Root Cause

Zoho has multiple data centers worldwide, and each data center only holds data for users registered in that region. When a user from the India data center tries to authenticate using the US endpoints, the token exchange fails because:

1. The authorization code is issued by the India data center (`accounts.zoho.in`)
2. But the token exchange request goes to the US data center (`accounts.zoho.com`)
3. The US data center doesn't recognize the authorization code from India

## Solution: Multi-DC Support

### 1. Data Center Configuration

Added `ZohoMultiDCConfig` class in `app/core/oauth_providers.py`:

```python
class ZohoDataCenter(str, Enum):
    US = "us"
    EU = "eu" 
    IN = "in"
    AU = "au"
    JP = "jp"
    CA = "ca"
    SA = "sa"
    UK = "uk"

class ZohoMultiDCConfig:
    DC_ENDPOINTS = {
        ZohoDataCenter.US: "https://accounts.zoho.com",
        ZohoDataCenter.EU: "https://accounts.zoho.eu",
        ZohoDataCenter.IN: "https://accounts.zoho.in",
        # ... other data centers
    }
```

### 2. Enhanced Token Exchange

Modified `OAuthService.exchange_code_for_tokens()` to handle Zoho Multi-DC:

```python
# Handle Zoho Multi-DC support
if provider == OAuthProvider.ZOHO:
    tokens = await self._handle_zoho_multidc_token_exchange(
        code, provider_config, state_data
    )
else:
    # Standard token exchange for other providers
    tokens = await self._standard_token_exchange(code, provider_config, provider)
```

### 3. Multi-DC Token Exchange Flow

The `_handle_zoho_multidc_token_exchange()` method implements the following flow:

1. **Try US data center first** (default)
2. **Check response for location parameter**
3. **If location indicates different DC, retry with correct endpoint**
4. **Return tokens with api_domain for future API calls**

```python
async def _handle_zoho_multidc_token_exchange(self, code, provider_config, state_data):
    # Try US data center first
    response = await client.post(provider_config.token_url, data=token_data)
    
    if response.status_code == 200:
        tokens = response.json()
        
        # Check if user is in different data center
        if "location" in tokens and tokens["location"].lower() != "us":
            dc = ZohoMultiDCConfig.detect_dc_from_location(tokens["location"])
            token_url = ZohoMultiDCConfig.get_token_url(dc)
            
            # Retry with correct data center
            response = await client.post(token_url, data=token_data)
            tokens = response.json()
        
        return tokens
```

## Key Features

### 1. Automatic Data Center Detection
- Detects user's data center from OAuth response
- Automatically retries with correct endpoint
- Supports all Zoho data centers (US, EU, IN, AU, JP, CA, SA, UK)

### 2. API Domain Preservation
- Stores `api_domain` from token response
- Enables correct API calls to user's data center
- Example: India users get `https://www.zohoapis.in`

### 3. Backward Compatibility
- No changes required for existing US users
- Other OAuth providers unaffected
- Graceful fallback to US if location unknown

### 4. Error Handling
- Comprehensive error handling for network issues
- Detailed logging for debugging
- Proper error messages for different failure scenarios

## Testing

Created comprehensive tests in `tests/test_zoho_multidc.py`:

- ✅ Data center configuration validation
- ✅ Location detection from response
- ✅ US user success flow
- ✅ India user redirect flow
- ✅ Error handling (invalid codes, network errors, timeouts)

## Configuration

### Environment Variables
No additional environment variables required. The existing Zoho OAuth configuration works:

```env
ZOHO_CLIENT_ID="your-zoho-client-id"
ZOHO_CLIENT_SECRET="your-zoho-client-secret"
ZOHO_REDIRECT_URI="http://localhost:8000/api/v1/oauth/callback"
```

### Zoho API Console Setup
1. Enable Multi-DC support in Zoho API Console
2. Go to Settings tab for your application
3. Enable required data centers (India, EU, etc.)
4. Choose whether to use same client secret for all DCs

## Benefits

1. **Global User Support**: Users from any Zoho data center can now authenticate
2. **Automatic Handling**: No manual configuration needed per user
3. **Performance**: Direct API calls to user's regional data center
4. **Compliance**: Respects data residency requirements
5. **Reliability**: Robust error handling and fallback mechanisms

## Migration

This is a backward-compatible change:
- Existing US users continue to work without changes
- India/EU users will now work correctly
- No database migrations required
- No API changes required

## Monitoring

Enhanced logging provides visibility into:
- Data center detection: `"Zoho user is in data center: in"`
- Retry attempts: `"Retrying token exchange with in data center"`
- Success confirmations: `"Successfully exchanged tokens with in data center"`

## References

- [Zoho Multi-DC Documentation](https://www.zoho.com/accounts/protocol/oauth/multi-dc.html)
- [Zoho Data Centers](https://www.zoho.com/know-your-datacenter.html)
- [Server URLs JSON](https://accounts.zoho.com/oauth/serverinfo)
